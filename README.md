# 大红袍官网主页

## 本地开发
```
npm install

npm run dev:doc
```

## 发布
xbrid: http://xbird.lizhi.fm/job/5f3f3366c92f6b4b49eeed55?name=%E5%9F%9F%E5%90%8D%E9%99%86%E7%BB%AD%E6%9C%89%E6%9D%A5%EF%BC%88%E5%A4%87%E6%A1%88%E9%A1%B5%E9%9D%A2%EF%BC%89

找到www.lzpipi.com-大红袍官网这份配置去构建

## 关于configCenter
这份文件在构建的时候会被替换，不需要去处理
相关配置在www.lzpipi.com中

## 其他静态资源信息
项目www_lzpipi_com

线上域名使用: www.lzpipi.com

预发域名使用: wwwpre.lzpipi.com

docker环境域名使用: wwwoffice.lzpipi.com

仓库地址: https://gitlab.lizhi.fm/www_static/www_lzpipi_com.git

请按以下分支上传对应资源:
线上: master分支

预发: pre分支

docker环境: dev分支