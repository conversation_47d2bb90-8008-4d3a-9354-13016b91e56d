const presets = [
    [
        '@babel/preset-env',
        {
            targets: { browsers: ['> 5%'] },
            useBuiltIns: 'usage'
        }
    ],
    '@babel/preset-react'
]

const plugins = [
    'react-hot-loader/babel',
    [require('@babel/plugin-proposal-class-properties'), { loose: true }],
    ['import', { libraryName: 'antd', style: 'css' }]
]

module.exports = {
    presets,
    plugins
}
