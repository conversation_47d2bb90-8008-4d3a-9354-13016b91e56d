const webpack = require('webpack')
const path = require('path')
const glob = require('glob')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const CleanWebpackPlugin = require('clean-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin')
const vConsoleWebpackPlugin = require('vconsole-webpack-plugin')
const autoprefixer = require('autoprefixer')
const pxtorem = require('postcss-pxtorem');
const XbirdPlugin = require('@lizhife/xbird-config-webpack-plugin')
const CopyWebpackPlugin = require('copy-webpack-plugin')


const NODE_ENV = process.env.NODE_ENV || 'development' // 构建环境
const PROJECT_ENV = process.env.PROJECT_ENV || 'prod' // 项目配置环境

const isDev = NODE_ENV === 'development'
const isProd = PROJECT_ENV === 'prod'

const entry = { index: path.resolve(__dirname, './src/index.js') }
const extraPlugins = [
  new HtmlWebpackPlugin({
    filename: path.resolve(__dirname, `./dist/index.html`),
    template: path.resolve(__dirname, `./src/index.html`),
    chunks: ['common', "index"]
	})
]

const output = {
	path: path.resolve(__dirname, './dist'),
	filename: `statics/js/[name]${isDev ? '' : '.[chunkhash:8]'}.js`,
	chunkFilename: `statics/js/[name]${isDev ? '' : '.[chunkhash:8]'}.chunk.js`
}

const devtool = isProd ? '' : '#source-map'

const resolve = {
	extensions: ['.jsx', '.js', '.json', '.less', '.css'],
	modules: [path.resolve(__dirname, 'node_modules'), 'node_modules'],
	alias: {
    'react-dom': '@hot-loader/react-dom',
		'@views': path.resolve(__dirname, 'src/views'),
		'@routes': path.resolve(__dirname, 'src/routes'),
		'@components': path.resolve(__dirname, 'src/components'),
		'@services': path.resolve(__dirname, 'src/services'),
    '@assets': path.resolve(__dirname, 'src/assets'),
    '@stores': path.resolve(__dirname, 'src/stores'),
    '@utils': path.resolve(__dirname, 'src/utils'),
    '@config': path.resolve(__dirname, 'src/config'),
    '@actions': path.resolve(__dirname, 'src/actions'),
	}
}

const optimization = {
    splitChunks: {
        name: 'common',
        chunks: 'initial'
    }
}

const modules = {
	rules: [
		{
			test: /\.(js|jsx)$/,
			exclude: /node_modules/,
			use: { loader: 'babel-loader' }
		},
		{
			test: /\.(css|less)$/,
			exclude: /node_modules/,
			use: [
				isDev ? 'style-loader' : {
					loader:MiniCssExtractPlugin.loader,
					options:{
						publicPath:"../../"
					}
				},
				{
					loader: 'css-loader',
					options: isDev ? { sourceMap: true } : { sourceMap: true }
				},
				{
					loader: `postcss-loader`,
					options: {
						sourceMap: isDev,
						ident: 'postcss',
						plugins: () => [
							autoprefixer({
								browsers: ['> 5%']
              }),
              pxtorem({
                propList: ['*'],
                mediaQuery: true
              })
						]
					}
				},
				{
					loader: 'less-loader',
					options: { sourceMap: isDev }
				}
			]
		},
		{
			test: /\.css$/,
			include: /node_modules/,
			use: ['style-loader', 'css-loader']
		},
		{
			test: /\.html$/,
			use: [{ loader: 'html-loader', options: { minimize: true } }]
		},
		{
			test: /\.(svg|woff2?|ttf|eot|jpe?g|png|gif|svga)(\?.*)?$/i,
			use: [
				{
					loader: 'url-loader',
					options: {
						limit: 5000,
						name: `./statics/media/[name]${
							isDev ? '' : '.[hash:8]'
						}.[ext]`
					}
				}
			]
		},
		{
			test: /\.(xml|txt|md)$/,
			use: 'raw-loader'
		}
	]
}

!isDev && extraPlugins.push(new OptimizeCSSAssetsPlugin({
    assetNameRegExp: /\.css$/g,
    cssProcessor: require('cssnano'),
    cssProcessorPluginOptions: {
        preset: [
            'default',
            {
                discardComments: { removeAll: true },
                map: { inline: false },
                normalizeUnicode: false
            }
        ]
     },
    canPrint: true
}))

const plugins = [
	new CleanWebpackPlugin(['dist'], { root: __dirname }),
	new webpack.DefinePlugin({
		'process.env.NODE_ENV': JSON.stringify(NODE_ENV),
		'process.env.PROJECT_ENV': JSON.stringify(PROJECT_ENV)
	}),
	new MiniCssExtractPlugin({
		filename: isDev
			? `/statics/css/[name].css`
			: `statics/css/[name].[contenthash].css`,
		chunkFilename: isDev
			? `statics/css/common.css`
			: `./statics/css/common.[contenthash].css`
	}),
	// new vConsoleWebpackPlugin({ enable: isDev }),
	new vConsoleWebpackPlugin({ enable: false }),
	new XbirdPlugin({
		configId: "<EMAIL>"
	}),
	new CopyWebpackPlugin([{ from: 'public', to: './' }]),
	// new webpack.HotModuleReplacementPlugin(),
	...extraPlugins
]

const devServer = {
	inline: true,
	hot: true,
	port: '9000',
	contentBase: path.resolve(__dirname, 'dist'),
	compress: false,
	historyApiFallback: true,
	proxy: {
		// '/luckyBag/*': {
		// 	target: 'http://yapi.feoffice.lizhi.fm/mock/57',
		// 	changeOrigin: true
		// }
	},
	overlay: false,
    stats: 'minimal',
	watchOptions: {
		ignored: [
			path.resolve(__dirname, 'dist'),
			path.resolve(__dirname, 'node_modules')
		]
	}
}

module.exports = {
	mode: NODE_ENV,
	entry,
	output,
	devtool,
	resolve,
	module: modules,
	optimization,
	plugins,
	devServer
}
