{"name": "dahongpao-homepage", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev:doc": "cross-env NODE_ENV=development PROJECT_ENV=doc webpack-dev-server --inline --progress --open", "dev:pre": "cross-env NODE_ENV=development PROJECT_ENV=pre webpack-dev-server --inline --hot --progress", "dev:prod": "cross-env NODE_ENV=development PROJECT_ENV=prod webpack-dev-server --inline --hot --progress", "build:doc": "cross-env NODE_ENV=production PROJECT_ENV=doc webpack --progress", "build:pre": "cross-env NODE_ENV=production PROJECT_ENV=pre webpack --progress", "build:prod": "cross-env NODE_ENV=production PROJECT_ENV=prod webpack --progress", "build": "cross-env NODE_ENV=production PROJECT_ENV=prod webpack --progress", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.3.3", "@babel/plugin-proposal-class-properties": "^7.3.3", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "@babel/preset-react": "^7.0.0", "@lizhife/xbird-config-webpack-plugin": "^1.1.3", "autoprefixer": "^9.4.8", "babel-loader": "^8.0.5", "babel-plugin-import": "^1.11.0", "clean-webpack-plugin": "^1.0.1", "copy-webpack-plugin": "^5.1.1", "cross-env": "^5.2.0", "css-loader": "^2.1.0", "cssnano": "^4.1.10", "file-loader": "^3.0.1", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "json-loader": "^0.5.7", "less": "^3.9.0", "less-loader": "^4.1.0", "mini-css-extract-plugin": "^0.9.0", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss-loader": "^3.0.0", "raw-loader": "^1.0.0", "react-hot-loader": "^4.7.1", "style-loader": "^0.23.1", "svgaplayerweb": "^2.3.0", "url-loader": "^1.1.2", "vconsole-webpack-plugin": "^1.4.2", "webpack": "^4.29.5", "webpack-cli": "^3.2.3", "webpack-dev-server": "^3.2.0"}, "dependencies": {"@hot-loader/react-dom": "^16.8.2", "antd": "^4.7.2", "axios": "^0.18.0", "history": "^4.7.2", "path-to-regexp": "^1.7.0", "postcss-pxtorem": "^4.0.1", "react": "^16.8.2", "react-dom": "^16.8.2", "react-qrcode-logo": "^2.2.1", "react-router-dom": "^4.3.1", "sweetalert2": "^8.14.0"}}