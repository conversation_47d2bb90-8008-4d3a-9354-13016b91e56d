import config from "config";

/**
 * 求数组元素之和
 * @param {Array} arr 数组
 */
export const arraySum = arr => arr && arr.length && arr.reduce((s, v) => s + v)

/**
 * 求数组对应元素之和
 * @param {Array} a 数组
 * @param {Array} b 数组
 */
export const arrayPlus = (a, b) => a.length === b.length && a.map((v, i) => v + b[i])

/**
 * 两对象相等(无嵌套, 子元素均为基本类型)
 * @param {Object} objA 对象A
 * @param {Object} ObjB 对象B
 */
export const equalObject = (objA, objB) => {
  if (Object.prototype.toString.call(objA) === "[object Object]" && Object.prototype.toString.call(objB) === "[object Object]") {
    let keysA = Object.keys(objA), keysB = Object.keys(objB);
    if (keysA.length === keysB.length) {
      let flag = keysA.some(key => objA[key] !== objB[key])
      return !flag
    }
  }
  return false
}

/**
 * 计算段位
 * @param {Number} stars 星数
 */
export const getLevelRange = stars => {
  const { LEVEL_RANGE_STARS } = config
  if (stars > LEVEL_RANGE_STARS[LEVEL_RANGE_STARS.length - 1]) {
      return LEVEL_RANGE_STARS.length
  }
  return LEVEL_RANGE_STARS.findIndex(item => item >= stars)
}

