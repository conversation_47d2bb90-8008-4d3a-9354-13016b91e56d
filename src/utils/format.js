export function timeLeftFormat(time) {
  let remainders = [86400000, 3600000, 60000, 1000]
  let dateUnits = ['天', '小时', '分', '秒',]
  let saveTime = []

  remainders.forEach((re, i) => {
    let hanleTime = time
    Array.from(new Array(i)).forEach((noThing, j) => {
      hanleTime %= remainders[j]
    })
    saveTime.push(parseInt(hanleTime / remainders[i]) + dateUnits[i])
    saveTime[i] >= 1
  })
  return saveTime.join('')
}

export function hmsFormat(timestamp) {
  if (timestamp) {
    const d = new Date(timestamp);
    const t = [d.getHours(), d.getMinutes(), d.getSeconds()];
    return t.map(v => {
      if (v / 10 >= 1) return v
      return `0${v}`
    }).join(':')
  } else {
    return '00:00:00'
  }
}

export function formatCount(count) {
  let countString = ''
  count = String(count)
  for (let i = count.length - 1, j = 0; i >= 0; i-- , j++) {
    countString = count[i] + countString
    if (j % 3 === 2 && i !== 0) {
      countString = ',' + countString
    }
  }
  return countString
}

export function remTranslatePx(rem) {
  return window.innerWidth * (Number(rem) / 3.75)
}

export function classFormat(...args) {
  const classArr = []
  args.forEach((v, i) => {
    v && classArr.push(v)
  })
  return classArr.join(' ')
}

export function setImageSize(url, size) {
  const reg = new RegExp(/\.(jpe?g|png)$/)
  if (reg.test(url)) {
    return url.replace(reg, suffix => `_${size}x${size}${suffix}`)
  }
  return url
}

export function isSameDay(day1, day2) {
  console.log(day1, day2)
  let date1, date2;
  if (!(day1 instanceof Date)) {
    date1 = new Date(Number(day1))
  } else {
    date1 = day1
  }
  if (!(day2 instanceof Date)) {
    date2 = new Date(Number(day2))
  } else {
    date2 = day2
  }

  if (date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate()) {
    return true
  }
  return false
}

export function formatDateTime(time, format) {
  let t = new Date(time);
  let tf = function (i) {
    return (i < 10 ? '0' : '') + i
  };
  // 处理映射
  let processMap = {
    'yyyy': tf(t.getFullYear()),
    'MM': tf(t.getMonth() + 1),
    'dd': tf(t.getDate()),
    'HH': tf(t.getHours()),
    'mm': tf(t.getMinutes()),
    'ss': tf(t.getSeconds())
  }
  return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
    return processMap[a]
  })
}
