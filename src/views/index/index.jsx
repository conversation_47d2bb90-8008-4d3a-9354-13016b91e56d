import React from 'react';
import { hot } from 'react-hot-loader/root';
import { Switch, Route, Redirect, BrowserRouter } from 'react-router-dom';
import Carousel from "@components/Carousel";
import Footer from "@components/Footer";
import Header from "@components/Header";
import './style';
import '../../assets/fonts/SourceHanSerifCN-Regular.css'

console.log('init')

const RouterApp = () => (
    <BrowserRouter>
      <section className="container">
        <Header />
        <Carousel />
        <Footer />
      </section>
    </BrowserRouter>
);

export default hot(RouterApp);
