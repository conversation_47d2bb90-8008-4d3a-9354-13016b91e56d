html {
  min-width: 1072px;
}

body {
  overflow: visible !important;
  padding-right: 0 !important;

  // background-position: 0 -100px;
  // background-size: 100% auto,100%;
}

#app {
  // width: 100vw;
  // min-height: 100vh;
  // height: 100%;
  // background-image: url('./images/bg-white.png');
  // background-position: left bottom,left top;
  // background-size: 100% auto,100%;
  display: flex;
  flex-direction: column;
}

.container {
  min-height: 100vh;
  background-image: url('./images/bg-white.png');
  background-size: 100% 100%;
  background-position: 0 58px;
}

.index {
  height: 100%;
  width: 100%;
  // width: 1024px;
  margin: 0 auto;
  overflow: scroll;
  flex: 1;
}

@media all and (orientation: portrait) {
  .index {
    width: 92%;
  }
}
