import React, { Component } from 'react'
import { Carousel as Swiper } from 'antd';
import Download from "@components/Download";
import './style';
import config from "@config";
import Swal from "sweetalert2";
import img1 from  './images/slide-1.png';
import img2 from  './images/slide-2.png';
import img3 from  './images/slide-3.png';

export default class Carousel extends Component {
  swiperRef = null

  downHandler = type => {
    let { downloadUrl } = config
    let url = downloadUrl[type]
    window.location.assign(url)
  }

  showToast = () => {
    Swal.fire({
      // toast: true,
      // width: 240,
      title: '开发中, 敬请期待',
      showConfirmButton: false,
      customClass: {
        title: 'toast-title'
      }
    })
  }

  render() {
    const { index } = this.props
    const settings = {
      dots: true,
      infinite: true,
      autoplay: true,
      autoplaySpeed: 2500,
      dotPosition: 'left',
      dots:{
        className: 'customDot'
      }
    };

    return <div className="carousel-wrap">
      <div className="content-container">
        <Swiper {...settings} ref={(ref => this.swiperRef = ref)}>
          <div className="swiper-item" >
            <img src={img1} alt=""/>
          </div>
          <div className="swiper-item" >
            <img src={img2} alt=""/>
          </div>
          <div className="swiper-item" >
            <img src={img3} alt=""/>
          </div>
        </Swiper>
      </div>

    </div>
  }
}
