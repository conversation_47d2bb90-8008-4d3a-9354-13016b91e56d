.carousel-wrap {
  width: 100%;
  min-width: 1260px;
  height: 780px;
  padding-top: 100px;
  border-radius: 20px;
  overflow: hidden;
  // background-position: 0px 30px;
  .content-container {
    position: relative;
    width: 793px;
    height: 100%;
    margin: 0 auto;
    background: url('./images/shadow.png')no-repeat 18px 22px;
    background-size: 673px 753px;
    .swiper-item{
      >img{
        width: 793px;
        height: 636px;
      }
    }
    .download-wrap {
      position: absolute;
      right: 49px;
      top: 323px;
      width: 280px;
    }
  }
}
.ant-carousel .slick-dots.customDot {
  margin-left: -240px;
  margin-top: 170px;
  &::after {
    content: "SHENG XIAN ";
    wrap-option: nowrap;
    position: absolute;
    left: -68px;
    top: -280px;
    width: 150px;
    line-height: 17px;
    transform: rotate(90deg);
    font-family: Arial;
    font-size: 16px;
    letter-spacing: 5.24px;
    color: #676c7b;
  }
  &::before {
    content: "";
    position: absolute;
    left: 6px;
    top: -196px;
    width: 1px;
    height: 96px;
    border: solid 1px #676c7b;
    transform: scale(0.5);
  }
}
.ant-carousel .slick-dots.customDot li button{
  width: 12px;
  height: 12px;
  border: 1px solid #767986;
  border-radius: 20px;
  opacity: 1;
}
.ant-carousel .slick-dots.customDot li.slick-active button {
  width: 12px;
  height: 18px;
  border-radius: 7px;
  background-color: #767986;
}
.toast-title {
  font-size: 24px !important;
  margin: 8px 0 !important;
}
