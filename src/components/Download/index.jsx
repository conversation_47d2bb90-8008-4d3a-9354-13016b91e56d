import React, { Component } from 'react'
import './style';
const appleQrcode = require('./image/apple-qrcode.png')
const androidQrcode = require('./image/android-qrcode.png')

export default class Download extends Component {
  state = {
    show: false,
    type: ''
  }

  timer = null

  show = (type) => {
    clearTimeout(this.timer)
    this.props.handleHover && this.props.handleHover();
    this.setState({
      show: true,
      type
    })
  }

  hide = () => {
    clearTimeout(this.timer)
    this.props.handleHide && this.props.handleHide();
    this.timer = setTimeout(() => {
      this.setState({
        show: false
      })
    }, 100);
  }

  render() {
    const { show, type } = this.state

    return (
      <div className="download-component">
        <div className="button-group">
          <div className="button-down apple" onMouseEnter={() => { this.show('apple') }} onMouseLeave={this.hide}>苹果下载</div>
          <div className="button-down android" onMouseEnter={() => { this.show('android') }} onMouseLeave={this.hide}>安卓下载</div>
        </div>
        {
          show && (
            <div className="popover" onMouseEnter={() => { this.show(type) }} onMouseLeave={this.hide}>
              <div className={`triangle ${type}`}></div>
              {
                type === 'apple' ? (
                  <div className="content">
                    <div className="title">IOS端下载方式</div>
                    <div className='download apple'><a href="https://client.lzpipi.com/common/get_app?platform=0" target="_blank">APP Store 下载页</a></div>
                    <div className="desc">在 App Store中搜索"荔枝播客”井下载扫描二维码并下载</div>
                    <img className="qrcode" src={appleQrcode} width={100} alt="qrcode"/>
                  </div>
                ) : (
                  <div className="content">
                    <div className="title">Android客户端下载方式</div>
                    <div className='download android'><a href="https://client.lzpipi.com/common/get_app?platform=11" download>点击直接下载到电脑本地</a></div>
                    <div className="desc">在各大安卓市场中搜索“荔枝播客”下载安装手机扫描二维码下载安装</div>
                    <img className="qrcode" src={androidQrcode} width={100} alt="qrcode"/>
                  </div>
                )
              }
            </div>
          )
        }
      </div>
    )
  }
}
