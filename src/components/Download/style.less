.download-component {
  .button-group {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    .button-down {
      box-sizing: border-box;
      width:  118px;
      height: 40px;
      margin-right: 24px;
      padding: 0 2px 0 38px;
      line-height: 40px;
      border: 1px solid #000;
      border-radius: 22px;
      background-repeat: no-repeat;
      letter-spacing: 1.75px;
      font-family: PingFangSC;
      font-size: 14px;
      background-position: 12px 9px;
      background-size: 20px 20px;
      &.android {
        margin-right: 0;
        border-color: #00b66a;
        color: #00b66a;
        background-image: url(./image/android.png);
        &:hover {
          background-image: url(./image/android-hover.png);
          background-color: #00b66a;
          color: #fff;
        }
      }
      &.apple {
        background-image: url(./image/apple.png);
        &:hover {
          background-image: url(./image/apple-hover.png);
          background-color: #000;
          color: #fff;
        }
      }
    }
  }
  .popover {
    left: -10px;
    top: 70px;
    position: absolute;
    .content {
      position: relative;
      box-sizing: border-box;
      width: 280px;
      height: 254px;
      padding: 12px 10px;
      box-shadow: 0 10px 40px 0 rgba(0, 0, 0, 0.05);
      background-color: #ffffff;
      border-radius: 12px;
      z-index: 2;
      .title {
        margin-bottom: 15px;
        font-family: PingFangSC;
        font-size: 12px;
        letter-spacing: 1.5px;
        color: #000000;
      }
      .download {
        cursor: pointer;
        display: inline-block;
        box-sizing: border-box;
        padding: 0 10px 0 28px;
        margin-bottom: 5px;
        font-family: PingFangSC;
        font-size: 12px;
        line-height: 29px;
        letter-spacing: 1.5px;
        color: #4c99fa;
        height: 29px;
        border-radius: 6px;
        border: solid 1px #509bf7;
        &.apple {
          background-image: url(./image/app-store.png);
          background-position: 5px 7px;
          background-size: 16px 16px;
          background-repeat: no-repeat;
        }
        &.android {
          background-image: url(./image/down.png);
          background-position: 6px 7px;
          background-size: 16px 16px;
          background-repeat: no-repeat;
        }
      }
      .desc {
        width: 237px;
        height: 44px;
        margin: 10px 3px 10px 0;
        font-family: PingFangSC;
        font-size: 12px;
        font-weight: 300;
        line-height: 1.83;
        letter-spacing: 1.5px;
        color: #000000;
      }
    }
    .triangle {
      z-index: 1;
      position: absolute;
      top: -18px;
      left: 50px;
      width: 0;
      height: 0;
      border: 9px solid transparent;
      border-bottom-color: #fff;
      filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.1));
      &.android {
        left: 190px;
      }
    }
  }
}