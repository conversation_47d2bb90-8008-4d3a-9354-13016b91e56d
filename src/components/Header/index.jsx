import React, { Component } from 'react'
import { withRouter } from 'react-router-dom';
import Download from "@components/Download";
const logo = require('./images/logo.png')

import './style';

const routes = [
  { label: '荔枝播客', path: '/' },
  // { label: '客户端下载', path: '/download' }
]
class Header extends Component {
  constructor(props) {
    super(props)

    this.state = {
      activeIndex: routes.findIndex(item => item.path === this.props.location.pathname)
    }
  }

  onChange = (index) => {
    this.setState({
      activeIndex: 0
    })
  }

  handleClick = (path, index) => {
    this.props.history.push(path)
    this.setState({
      activeIndex: index
    })
  }

  render() {
    return <div className="header">
      {/* <div className="logo" onClick={this.logoClick}></div> */}
      <div className="tab-bar">
        <div className="tab-btn-container">
          <a href="/">
            <img src={logo} width={116} height={28} alt="荔枝播客"/>
          </a>
          <div className='tab-download-wrap'>
            <Download />
          </div>
        </div>
      </div>
    </div>
  }
}

export default withRouter(Header)
