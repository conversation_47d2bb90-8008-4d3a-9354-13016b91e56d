import axios from 'axios'
import config from 'config'


export function getJson(url, params = {}) {
  return axios(url, { params, timeout: config.reqTimeout })
}

export function postForm(url, data = {}) {
  return axios({
    method: 'post',
    url,
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    transformRequest: [function (data) {
      let ret = ''
      for (let it in data) {
        ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
      }
      return ret
    }]
  })
}
